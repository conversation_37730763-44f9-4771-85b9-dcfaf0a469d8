"""
Composio Integration Configuration

This file contains the mapping of services to their Composio integration IDs,
descriptions, and MCP server configurations.
"""

from typing import Dict, List, Optional
from pydantic import BaseModel


class ComposioIntegration(BaseModel):
    """Configuration for a Composio integration."""

    service_name: str
    integration_id: str  # From Composio dashboard
    display_name: str
    description: str
    category: str
    icon_url: Optional[str] = None
    auth_type: str = "oauth2"
    scopes: List[str] = []
    mcp_app_names: List[str] = []  # App names for MCP server creation

    # NEW FIELDS for scalable dynamic system
    xml_tag_name: str  # e.g., "gmail-mcp", "notion-mcp"
    enabled: bool = True
    tool_description: str  # For system prompt generation
    requires_connection: bool = True


# Integration configurations from your Composio dashboard
COMPOSIO_INTEGRATIONS: Dict[str, ComposioIntegration] = {
    "gmail": ComposioIntegration(
        service_name="gmail",
        integration_id="c806ea76-3258-4c41-a9a9-784a77fad00d",  # Your Gmail integration ID
        display_name="Gmail",
        description="Connect to Gmail to read, send, and manage emails",
        category="email",
        icon_url="https://example.com/gmail-icon.png",
        auth_type="oauth2",
        scopes=["https://www.googleapis.com/auth/gmail.modify"],
        mcp_app_names=["gmail"],
        # NEW FIELDS
        xml_tag_name="gmail-mcp",
        enabled=True,
        tool_description="Access Gmail through MCP with user authentication for sending, reading, and managing emails",
        requires_connection=True,
    ),
    # Add more integrations as you create them in Composio dashboard
    # "googledrive": ComposioIntegration(
    #     service_name="googledrive",
    #     integration_id="your-drive-integration-id",
    #     display_name="Google Drive",
    #     description="Connect to Google Drive to manage files and folders",
    #     category="storage",
    #     auth_type="oauth2",
    #     scopes=["https://www.googleapis.com/auth/drive"],
    #     mcp_app_names=["googledrive"],
    #     # NEW FIELDS
    #     xml_tag_name="googledrive-mcp",
    #     enabled=True,
    #     tool_description="Access Google Drive through MCP with user authentication for managing files and folders",
    #     requires_connection=True,
    # ),
    "notion": ComposioIntegration(
        service_name="notion",
        integration_id="92ae7e87-2f68-4401-b63e-403115a4af59",  # From user's dashboard logs
        display_name="Notion",
        description="Connect to Notion to manage databases and pages",
        category="productivity",
        icon_url="https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/notion.svg",
        auth_type="oauth2",
        scopes=["read", "write"],
        mcp_app_names=["notion"],
        # NEW FIELDS
        xml_tag_name="notion-mcp",
        enabled=True,
        tool_description="Access Notion through MCP with user authentication for managing pages, databases, and content",
        requires_connection=True,
    ),
}


def get_integration(service_name: str) -> Optional[ComposioIntegration]:
    """Get integration configuration by service name."""
    return COMPOSIO_INTEGRATIONS.get(service_name.lower())


def get_all_integrations() -> List[ComposioIntegration]:
    """Get all available integrations."""
    return list(COMPOSIO_INTEGRATIONS.values())


def get_integration_by_id(integration_id: str) -> Optional[ComposioIntegration]:
    """Get integration configuration by integration ID."""
    for integration in COMPOSIO_INTEGRATIONS.values():
        if integration.integration_id == integration_id:
            return integration
    return None
