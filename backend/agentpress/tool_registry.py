from typing import Dict, Type, Any, List, Optional, Callable, Union
from agentpress.tool import Tool, SchemaType
from utils.logger import logger
import importlib
import os
import logging
import json
from pathlib import Path

logger = logging.getLogger(__name__)


class ToolRegistry:
    """Registry for managing and accessing tools.

    Maintains a collection of tool instances and their schemas, allowing for
    selective registration of tool functions and easy access to tool capabilities.

    Attributes:
        tools (Dict[str, Dict[str, Any]]): OpenAPI-style tools and schemas
        xml_tools (Dict[str, Dict[str, Any]]): XML-style tools and schemas
        openai_tools (List[Dict[str, Any]]): OpenAI function tools

    Methods:
        register_tool: Register a tool with optional function filtering
        register_openai_tools: Register OpenAI function tools
        get_tool: Get a specific tool by name
        get_xml_tool: Get a tool by XML tag name
        get_openai_tool: Get an OpenAI function tool by name
        get_openapi_schemas: Get OpenAPI schemas for function calling
        get_xml_examples: Get examples of XML tool usage
        get_openai_tools: Get all registered OpenAI function tools
    """

    def __init__(self):
        """Initialize a new ToolRegistry instance."""
        self._tools = {}
        self._schemas = []
        self.xml_tools = {}
        self.openai_tools = []

        logger.info("Initializing ToolRegistry")
        self._load_tools()

        logger.debug(
            f"Initialized ToolRegistry with {len(self._tools)} tools, {len(self._schemas)} schemas, "
            f"{len(self.xml_tools)} XML tools, and {len(self.openai_tools)} OpenAI tools"
        )

    def _load_tools(self):
        """Load all tools from the agent/tools directory."""
        try:
            # Get the tools directory path
            tools_dir = Path(__file__).parent.parent / "agent" / "tools"
            logger.info(f"Loading tools from: {tools_dir}")
            if not tools_dir.exists():
                logger.error(f"Tools directory not found: {tools_dir}")
                return

            # List all Python files in the tools directory
            tool_files = [
                f
                for f in tools_dir.glob("*.py")
                if f.is_file() and f.name != "__init__.py"
            ]
            logger.info(
                f"Found {len(tool_files)} tool files: {[f.name for f in tool_files]}"
            )

            for tool_file in tool_files:
                try:
                    # Convert file path to module path
                    module_name = f"agent.tools.{tool_file.stem}"
                    logger.debug(f"Attempting to load module: {module_name}")

                    # Import the module
                    module = importlib.import_module(module_name)
                    logger.debug(f"Successfully imported module: {module_name}")

                    # Look for tool classes
                    tool_classes_found = 0
                    for attr_name in dir(module):
                        attr = getattr(module, attr_name)

                        # Check if it's a class that inherits from Tool
                        if (
                            isinstance(attr, type)
                            and issubclass(attr, Tool)
                            and attr != Tool
                        ):
                            try:
                                logger.debug(
                                    f"Found tool class: {attr_name} in {module_name}"
                                )

                                # Check if the tool requires constructor parameters
                                import inspect

                                init_signature = inspect.signature(attr.__init__)
                                required_params = [
                                    param
                                    for param_name, param in init_signature.parameters.items()
                                    if param_name != "self"
                                    and param.default == inspect.Parameter.empty
                                ]

                                if required_params:
                                    logger.debug(
                                        f"Tool {attr_name} requires constructor parameters: {[p.name for p in required_params]}. Skipping auto-registration."
                                    )
                                    continue

                                # Instantiate the tool (only if no required parameters)
                                tool_instance = attr()
                                schemas = tool_instance.get_schemas()
                                logger.debug(
                                    f"Tool class {attr_name} has {len(schemas)} schemas: {list(schemas.keys())}"
                                )

                                for func_name, schema_list in schemas.items():
                                    for schema in schema_list:
                                        if schema.schema_type == SchemaType.OPENAPI:
                                            logger.info(
                                                f"Registering OpenAPI tool: {func_name} from {tool_file.name}"
                                            )
                                            self._tools[func_name] = {
                                                "instance": tool_instance,
                                                "schema": schema,
                                            }
                                            schema_dict = {
                                                "type": "function",
                                                "function": {
                                                    "name": func_name,
                                                    "description": schema.schema.get(
                                                        "function", {}
                                                    ).get("description", ""),
                                                    "parameters": schema.schema.get(
                                                        "function", {}
                                                    ).get("parameters", {}),
                                                },
                                            }
                                            self._schemas.append(schema_dict)
                                            logger.debug(
                                                f"Added schema for {func_name}: {schema_dict}"
                                            )
                                tool_classes_found += 1
                            except Exception as tool_init_error:
                                logger.error(
                                    f"Error initializing tool {attr_name} from {tool_file}: {tool_init_error}",
                                    exc_info=True,
                                )
                                continue
                    logger.info(
                        f"Found {tool_classes_found} tool classes in {module_name}"
                    )

                except Exception as module_error:
                    logger.error(
                        f"Error loading module from {tool_file}: {module_error}",
                        exc_info=True,
                    )
                    continue

            logger.info(
                f"Successfully loaded {len(self._tools)} tools with {len(self._schemas)} schemas"
            )

        except Exception as e:
            logger.error(f"Error in tool loading process: {e}", exc_info=True)
            raise

    def register_tool(
        self,
        tool_class: Type[Tool],
        function_names: Optional[List[str]] = None,
        **kwargs,
    ):
        """Register a tool with optional function filtering.

        Args:
            tool_class: The tool class to register
            function_names: Optional list of specific functions to register
            **kwargs: Additional arguments passed to tool initialization

        Notes:
            - If function_names is None, all functions are registered
            - Handles both OpenAPI and XML schema registration
        """
        logger.debug(f"Registering tool class: {tool_class.__name__}")
        tool_instance = tool_class(**kwargs)
        schemas = tool_instance.get_schemas()

        logger.debug(
            f"Available schemas for {tool_class.__name__}: {list(schemas.keys())}"
        )

        registered_openapi = 0
        registered_xml = 0

        for func_name, schema_list in schemas.items():
            if function_names is None or func_name in function_names:
                for schema in schema_list:
                    if schema.schema_type == SchemaType.OPENAPI:
                        self._tools[func_name] = {
                            "instance": tool_instance,
                            "schema": schema,
                        }
                        registered_openapi += 1
                        logger.debug(
                            f"Registered OpenAPI function {func_name} from {tool_class.__name__}"
                        )

                    if schema.schema_type == SchemaType.XML and schema.xml_schema:
                        self.xml_tools[schema.xml_schema.tag_name] = {
                            "instance": tool_instance,
                            "method": func_name,
                            "schema": schema,
                        }
                        registered_xml += 1
                        logger.debug(
                            f"Registered XML tag {schema.xml_schema.tag_name} -> {func_name} from {tool_class.__name__}"
                        )

        logger.debug(
            f"Tool registration complete for {tool_class.__name__}: {registered_openapi} OpenAPI functions, {registered_xml} XML tags"
        )

    def get_available_functions(self) -> Dict[str, Callable]:
        """Get all available tool functions.

        Returns:
            Dict mapping function names to their implementations
        """
        available_functions = {}

        # Get OpenAPI tool functions
        for tool_name, tool_info in self._tools.items():
            tool_instance = tool_info["instance"]
            function_name = tool_name
            function = getattr(tool_instance, function_name)
            available_functions[function_name] = function

        # Get XML tool functions
        for tag_name, tool_info in self.xml_tools.items():
            tool_instance = tool_info["instance"]
            method_name = tool_info["method"]
            function = getattr(tool_instance, method_name)
            available_functions[method_name] = function

        logger.debug(f"Retrieved {len(available_functions)} available functions")
        return available_functions

    def get_tool(self, tool_name: str) -> Dict[str, Any]:
        """Get a specific tool by name.

        Args:
            tool_name: Name of the tool function

        Returns:
            Dict containing tool instance and schema, or empty dict if not found
        """
        tool = self._tools.get(tool_name, {})
        if not tool:
            logger.warning(f"Tool not found: {tool_name}")
        return tool

    def get_xml_tool(self, tag_name: str) -> Dict[str, Any]:
        """Get tool info by XML tag name.

        Args:
            tag_name: XML tag name for the tool

        Returns:
            Dict containing tool instance, method name, and schema
        """
        tool = self.xml_tools.get(tag_name, {})
        if not tool:
            logger.warning(f"XML tool not found for tag: {tag_name}")
        return tool

    def get_openapi_schemas(self) -> List[Dict[str, Any]]:
        """Get OpenAPI schemas for all registered tools."""
        logger.debug(f"Returning {len(self._schemas)} OpenAPI schemas")
        return self._schemas

    def get_xml_examples(self) -> List[Dict[str, Any]]:
        """Get examples of XML tool usage.

        Returns:
            List of dictionaries containing tag names and example usage.
        """
        examples = []
        for tool_name, tool_info in self.xml_tools.items():
            if "example" in tool_info and tool_info["example"]:
                examples.append(
                    {
                        "tag": tool_name,
                        "example": tool_info["example"],
                    }
                )
        return examples

    def register_openai_tools(
        self, tools: List[Dict[str, Any]], mark_as_composio: bool = False
    ):
        """Register OpenAI function tools.

        Args:
            tools: List of OpenAI function tool definitions.
            mark_as_composio: Whether to mark these tools as Composio tools.
        """
        if not tools:
            logger.warning("No OpenAI tools provided for registration")
            return

        # Log the tool registration
        tool_names = [tool.get("function", {}).get("name", "unknown") for tool in tools]
        logger.debug(f"Registering {len(tools)} OpenAI tools: {tool_names}")

        # Add tools to the registry
        for tool in tools:
            # Check if the tool is already registered
            tool_name = tool.get("function", {}).get("name")
            if tool_name:
                # Mark as Composio tool if requested
                if mark_as_composio:
                    tool["composio_tool"] = True
                # Check for duplicates by name
                existing_tool_index = next(
                    (
                        i
                        for i, t in enumerate(self.openai_tools)
                        if t.get("function", {}).get("name") == tool_name
                    ),
                    None,
                )

                if existing_tool_index is not None:
                    # Replace existing tool
                    self.openai_tools[existing_tool_index] = tool
                    logger.debug(f"Replaced existing OpenAI tool: {tool_name}")
                else:
                    # Add new tool
                    self.openai_tools.append(tool)
                    logger.debug(f"Added new OpenAI tool: {tool_name}")
            else:
                logger.warning(f"Skipping OpenAI tool with no name: {tool}")

        logger.info(f"Successfully registered {len(tools)} OpenAI tools")

    def get_openai_tools(self) -> List[Dict[str, Any]]:
        """Get all registered OpenAI function tools.

        Returns:
            List of OpenAI function tool definitions.
        """
        return self.openai_tools

    def get_openai_tool(self, name: str) -> Optional[Dict[str, Any]]:
        """Get an OpenAI function tool by name.

        Args:
            name: The name of the tool to retrieve.

        Returns:
            The tool definition if found, None otherwise.
        """
        for tool in self.openai_tools:
            if tool.get("function", {}).get("name") == name:
                return tool
        return None

    def execute_openai_tool(
        self,
        name: str,
        arguments: Union[str, Dict[str, Any]],
        user_id: Optional[str] = None,
    ):
        """Execute an OpenAI function tool.

        Args:
            name: The name of the tool to execute.
            arguments: The arguments to pass to the tool, either as a JSON string or a dictionary.
            user_id: The user ID for Composio tools (required for Composio tools).

        Returns:
            The result of the tool execution.
        """
        logger.debug(f"Attempting to execute OpenAI tool: {name}")

        # Find the tool by name
        tool = self.get_openai_tool(name)
        if not tool:
            logger.error(f"OpenAI tool not found: {name}")
            return {"error": f"Tool '{name}' not found"}

        # Parse arguments if needed
        if isinstance(arguments, str):
            try:
                arguments = json.loads(arguments)
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse arguments for tool {name}: {e}")
                return {"error": f"Invalid arguments: {e}"}

        # Execute the tool
        try:
            # Check if this is a Composio tool (has composio_tool metadata)
            if tool.get("composio_tool", False):
                if not user_id:
                    logger.error(f"User ID required for Composio tool {name}")
                    return {"error": f"User ID required for Composio tool '{name}'"}

                # Delegate to Composio service
                from services.composio_openai_service import ComposioOpenAIService

                composio_service = ComposioOpenAIService.from_env()

                # Parse arguments if they're a JSON string
                if isinstance(arguments, str):
                    try:
                        arguments = json.loads(arguments)
                    except json.JSONDecodeError:
                        logger.error(
                            f"Failed to parse arguments for tool {name}: {arguments}"
                        )
                        return {"error": f"Invalid arguments format for tool '{name}'"}

                # Execute the tool directly
                result = composio_service.execute_tool(user_id, name, arguments)
                return result

            # For non-Composio tools, use the original execution method
            if "execute" in tool:
                return tool["execute"](arguments)
            else:
                logger.error(f"No execute method found for tool {name}")
                return {"error": f"Tool '{name}' has no execute method"}
        except Exception as e:
            logger.error(f"Error executing OpenAI tool {name}: {e}")
            return {"error": f"Execution failed: {e}"}

    def list_tools(self) -> List[str]:
        """List all registered tool names."""
        tool_list = list(self._tools.keys())
        logger.debug(f"Listed {len(tool_list)} tools: {tool_list}")
        return tool_list
